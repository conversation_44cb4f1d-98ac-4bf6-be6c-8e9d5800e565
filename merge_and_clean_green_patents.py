#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上市公司绿色专利数据合并与清洗脚本
功能：
1. 合并2014-2023年的绿色专利数据
2. 进行数据清洗和预处理
3. 输出清洗后的数据
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_merge_data():
    """
    加载并合并2014-2023年的绿色专利数据
    """
    print("开始加载数据文件...")
    
    all_data = []
    years = range(2014, 2024)
    
    for year in years:
        file_path = f'csv_data/上市公司、子公司绿色专利数据{year}.xlsx'
        
        if os.path.exists(file_path):
            try:
                print(f"正在读取 {year} 年数据...")
                df = pd.read_excel(file_path)
                
                # 添加数据年份标识
                df['数据年份'] = year
                
                all_data.append(df)
                print(f"{year}年数据加载完成，共 {len(df)} 条记录")
                
            except Exception as e:
                print(f"读取 {year} 年数据时出错: {e}")
        else:
            print(f"{year} 年数据文件不存在")
    
    if not all_data:
        raise ValueError("没有找到任何数据文件")
    
    # 合并所有数据
    print("\n正在合并数据...")
    merged_data = pd.concat(all_data, ignore_index=True)
    print(f"数据合并完成，总计 {len(merged_data)} 条记录")
    
    return merged_data

def clean_data(df):
    """
    数据清洗函数
    """
    print("\n开始数据清洗...")
    
    # 记录原始数据量
    original_count = len(df)
    print(f"原始数据量: {original_count}")
    
    # 1. 处理列名中的空格和特殊字符
    df.columns = df.columns.str.strip()
    
    # 2. 处理关联股票代码列名问题（可能有空格）
    stock_code_cols = [col for col in df.columns if '股票代' in col]
    if stock_code_cols:
        df = df.rename(columns={stock_code_cols[0]: '关联股票代码'})
    
    # 3. 处理专利英文名称列名问题（可能有空格）
    english_name_cols = [col for col in df.columns if '专利英' in col and '名称' in col]
    if english_name_cols:
        df = df.rename(columns={english_name_cols[0]: '专利英文名称'})
    
    # 4. 删除完全重复的记录
    before_dedup = len(df)
    df = df.drop_duplicates()
    after_dedup = len(df)
    print(f"删除完全重复记录: {before_dedup - after_dedup} 条")
    
    # 5. 处理关键字段的缺失值
    print("\n处理缺失值...")
    
    # 删除关键字段为空的记录
    key_fields = ['专利申请号', '申请人', '专利中文名称']
    for field in key_fields:
        if field in df.columns:
            before_count = len(df)
            df = df.dropna(subset=[field])
            after_count = len(df)
            if before_count != after_count:
                print(f"删除{field}为空的记录: {before_count - after_count} 条")
    
    # 6. 处理日期字段
    print("\n处理日期字段...")
    date_fields = ['申请日期', '公开日期', '授权日期', '最早优先权日期']
    
    for field in date_fields:
        if field in df.columns:
            # 将数字格式的日期转换为标准日期格式
            df[field] = pd.to_numeric(df[field], errors='coerce')
            
            # 处理明显错误的日期（如小于1900年或大于当前年份）
            current_year = datetime.now().year
            mask = (df[field] >= 19000101) & (df[field] <= current_year * 10000 + 1231)
            df.loc[~mask, field] = np.nan
    
    # 7. 处理年份字段
    print("处理年份字段...")
    year_fields = ['申请年份', '公开年份', '授权年份']
    
    for field in year_fields:
        if field in df.columns:
            df[field] = pd.to_numeric(df[field], errors='coerce')
            
            # 处理明显错误的年份
            current_year = datetime.now().year
            mask = (df[field] >= 1900) & (df[field] <= current_year)
            df.loc[~mask, field] = np.nan
    
    # 8. 标准化文本字段
    print("标准化文本字段...")
    text_fields = ['绿色专利大类', '绿色专利中类', '绿色专利小类', '专利类型', '专利状态', 
                   '与上市公司关系', '上市公司行业', '企业注册地']
    
    for field in text_fields:
        if field in df.columns:
            # 去除前后空格
            df[field] = df[field].astype(str).str.strip()
            # 将空字符串转换为NaN
            df[field] = df[field].replace('', np.nan)
            df[field] = df[field].replace('nan', np.nan)
    
    # 9. 处理股票代码格式
    if '关联股票代码' in df.columns:
        print("处理股票代码格式...")
        # 去除股票代码中的非数字字符，保留6位数字
        df['关联股票代码'] = df['关联股票代码'].astype(str).str.extract('(\d{6})')
    
    # 10. 删除基于专利申请号的重复记录（保留最新的记录）
    if '专利申请号' in df.columns and '数据年份' in df.columns:
        print("删除基于专利申请号的重复记录...")
        before_patent_dedup = len(df)
        df = df.sort_values('数据年份', ascending=False)
        df = df.drop_duplicates(subset=['专利申请号'], keep='first')
        after_patent_dedup = len(df)
        print(f"删除专利申请号重复记录: {before_patent_dedup - after_patent_dedup} 条")
    
    # 11. 重置索引
    df = df.reset_index(drop=True)
    
    final_count = len(df)
    print(f"\n数据清洗完成!")
    print(f"原始记录数: {original_count}")
    print(f"清洗后记录数: {final_count}")
    print(f"删除记录数: {original_count - final_count}")
    print(f"数据保留率: {final_count/original_count*100:.2f}%")
    
    return df

def generate_summary_report(df):
    """
    生成数据摘要报告
    """
    print("\n" + "="*50)
    print("数据摘要报告")
    print("="*50)
    
    # 基本信息
    print(f"总记录数: {len(df):,}")
    print(f"总列数: {len(df.columns)}")
    
    # 按年份统计
    if '数据年份' in df.columns:
        print("\n按年份统计:")
        year_stats = df['数据年份'].value_counts().sort_index()
        for year, count in year_stats.items():
            print(f"  {year}年: {count:,} 条")
    
    # 按绿色专利大类统计
    if '绿色专利大类' in df.columns:
        print("\n按绿色专利大类统计:")
        category_stats = df['绿色专利大类'].value_counts().head(10)
        for category, count in category_stats.items():
            print(f"  {category}: {count:,} 条")
    
    # 按专利类型统计
    if '专利类型' in df.columns:
        print("\n按专利类型统计:")
        type_stats = df['专利类型'].value_counts()
        for ptype, count in type_stats.items():
            print(f"  {ptype}: {count:,} 条")
    
    # 缺失值统计
    print("\n主要字段缺失值统计:")
    missing_stats = df.isnull().sum()
    important_fields = ['关联股票代码', '申请日期', '授权日期', '专利状态']
    for field in important_fields:
        if field in df.columns:
            missing_count = missing_stats[field]
            missing_rate = missing_count / len(df) * 100
            print(f"  {field}: {missing_count:,} ({missing_rate:.2f}%)")

def main():
    """
    主函数
    """
    try:
        # 1. 加载并合并数据
        merged_data = load_and_merge_data()
        
        # 2. 数据清洗
        cleaned_data = clean_data(merged_data)
        
        # 3. 生成摘要报告
        generate_summary_report(cleaned_data)
        
        # 4. 保存清洗后的数据
        output_file = 'csv_data/cleaned_green_patents_2014_2023.xlsx'
        print(f"\n正在保存清洗后的数据到: {output_file}")
        cleaned_data.to_excel(output_file, index=False)
        print("数据保存完成!")
        
        # 5. 同时保存为CSV格式
        csv_output_file = 'csv_data/cleaned_green_patents_2014_2023.csv'
        print(f"同时保存CSV格式到: {csv_output_file}")
        cleaned_data.to_csv(csv_output_file, index=False, encoding='utf-8-sig')
        print("CSV文件保存完成!")
        
        return cleaned_data
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return None

if __name__ == "__main__":
    result = main()
    if result is not None:
        print("\n处理完成! 清洗后的数据已保存。")
    else:
        print("\n处理失败，请检查错误信息。")
