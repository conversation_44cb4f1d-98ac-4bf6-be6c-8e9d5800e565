from datasets import load_dataset
import os

# Load the dataset from Hugging Face
print("正在加载数据集...")
dataset = load_dataset("cwinkler/green_patents", split="train")

# Convert to pandas DataFrame
print("正在转换为DataFrame...")
df = dataset.to_pandas()

# Display basic information about the dataset
print(f"数据集形状: {df.shape}")
print(f"列名: {list(df.columns)}")
print("\n前5行数据:")
print(df.head())

# Save to CSV file
output_filename = "green_patents_data.csv"
print(f"\n正在保存数据到 {output_filename}...")
df.to_csv(output_filename, index=False, encoding='utf-8')

print(f"数据已成功保存到 {output_filename}")
print(f"文件大小: {os.path.getsize(output_filename) / (1024*1024):.2f} MB")