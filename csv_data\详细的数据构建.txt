从do 中复制粘贴而来
* 公司规模	Size	总资产的自然对数
gen Size=ln(资产总计)
label var Size "公司规模"

* 资产负债率	Lev 
gen Lev=负债合计/资产总计
label var Lev "资产负债率"

* 总资产净利润率	ROA	
gen ROA= 总资产净利润率ROAB
label var ROA "总资产净利润率"

* 净资产收益率	ROE	
gen ROE=净资产收益率ROEB
label var ROE "净资产收益率"

* 总资产周转率	ATO	营业收入/平均资产总额
gen ATO=总资产周转率B
label var ATO "总资产周转率"

* 现金流比率	Cashflow	经营活动产生的现金流量净额除以总资产
gen Cashflow=经营活动产生的现金流量净额/资产总计
label var Cashflow "现金流比率"

* 存货占比	INV	存货净额与总资产的比值
gen INV=存货净额/资产总计
label var INV "存货占比"

* 固定资产占比	FIXED	固定资产净额与总资产比值
gen FIXED=固定资产净额/资产总计
label var FIXED "固定资产占比"

* 营业收入增长率	Growth	本年营业收入/上一年营业收入-1
gen Growth=营业收入增长率B
label var Growth "营业收入增长率"

* 是否亏损	Loss	当年净利润小于0取 1，否则取0
gen Loss=(净利润<0) if 净利润!=.
label var Loss "是否亏损"

* 董事规模	Board	董事会人数取自然对数
gen Board=ln(董事会规模)
label var Board "董事人数"

* 独立董事占比	Indep	独立董事除以董事人数
gen Indep=独立董事占比
label var Indep "独立董事占比"

* 两职合一	Dual	董事长与总经理是同一个人为l，否则为0
gen Dual=(董事长与总经理兼任情况==1)  
label var Dual "两职合一"

* 第一大股东持股比例	Top1	第一大股东持股数量/总股数
gen Top1=股权集中指标1/100
label var Top1 "第一大股东持股比例"

* 前五大股东持股比例	Top5	前五股东持股数量/总股数
gen Top5=股权集中指标3/100
label var Top5 "前五大股东持股比例"

* 前十大股东持股比例	Top10	前十股东持股数量/总股数
gen Top10=股权集中指标4/100
label var Top10 "前十大股东持股比例"

* 股权制衡度	Balance1	第二大股东持股比例除以第一大股东持股比例
gen Balance1=1/Z指数
label var Balance1 "股权制衡度"

* 股权制衡度	Balance2	第二到五位大股东持股比例的和除以第一大股东持股比例
gen Balance2=(股权集中指标3-股权集中指标1)/股权集中指标1
label var Balance2 "股权制衡度"


* 托宾Q值
gen TobinQ=托宾Q值A
label var TobinQ "托宾Q值"

* 股权性质 SOE 是否国有企业
gen 是否国有=( regexm(企业性质,"国企"))
gen SOE=是否国有
label var SOE "股权性质"

* 上市年限	ListAge	ln(当年年份-上市年份+1)
gen ListAge=ln(year-real(substr(上市日期,1,4))+1)
label var ListAge "上市年限"

* 公司成立年限	FirmAge	ln(当年年份-公司成立年份+1)
gen FirmAge=ln(year-real(substr(成立日期,1,4))+1)
label var FirmAge "公司成立年限"


* 机构投资者持股比例	INST	机构投资者持股总数除以流通股本
gen INST=机构投资者持股比例/100
label var INST "机构投资者持股比例"

* 管理层持股比例	Mshare	管理层持股数据除以总股本
gen Mshare=管理层持股比例
label var Mshare "管理层持股比例"

* 管理费用率	Mfee	管理费用除以营业收入
gen Mfee=管理费用/营业收入
label var Mfee "管理费用率"

* 大股东资金占用	Occupy	其他应收款除以总资产
gen Occupy=其他应收款/资产总计
label var Occupy "大股东资金占用"


* 是否四大	Big4	公司经由四大（普华永道、德勤、毕马威、安永）审计为1，否则为0。
gen Big4=是否四大会计事务所
label var Big4 "是否四大"

* 行业		证监会2012年行业分类，制造业取两位代码，其他行业用大类
gen Industry=substr(行业代码, 1, 1)
replace  Industry=substr(行业代码, 1, 2) if Industry=="C"
label var Industry "行业"

*代理成本 AgC1_* AgC2 
*第一类代理成本
gen  AgC1_1=管理费用/营业收入
label var AgC1_1 "第一类代理成本-管理费用率"
  *经营费用率 管理费用与销售费用之和与营业收入的比值
gen  AgC1_2=(管理费用+销售费用)/营业收入
label var AgC1_2 "第一类代理成本-经营费用率"
  *总资产周转率
gen  AgC1_3=营业收入/((资产总计+L.资产总计)/2)
label var AgC1_3 "第一类代理成本-总资产周转率"
*第二类代理成本
gen  AgC2=其他应收款净额/资产总计
label var AgC2 "第二类代理成本"

*公司人数 Employ  员工人数
gen Employ=员工人数
label var Employ "员工总人数"
 
*资本密集度  Cap1  资产总计/员工人数/ Cap2 资产总计/营业收入
gen Cap1=ln(资产总计/员工人数)
label var Cap1 "资产总计/员工人数"
gen Cap2=资本密集度
label var Cap2"资产总计/营业收入"

*高管薪酬激励  Pay 管理层年度薪酬总额的自然对数
gen Pay=ln(管理层薪酬总额)
